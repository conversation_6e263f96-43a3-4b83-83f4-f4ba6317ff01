export interface UserSocialNetwork {
  email?: string;
  thumbnail?: string | null;
  background?: string | null;
  phoneNumber?: string | null;
  job?: string | null;
  education?: string | null;
  bio?: string | null;
  fullName: string | null;
  firstName: string;
  lastName: string;
  gender?: string | null;
  dateOfBirth?: string | null;
  address?: string | null;
  orgName?: string | null;
  numberOfPost?: number;
  numberOfLike?: number;
  numberOfFriend?: number;
}

export interface UserSocialNetworkResponse {
  statusCode: number;
  responseCode: string | null;
  responseMessage: string;
  data: UserSocialNetwork;
}

export interface UserHistorySocialNetwork {
  content: string;
  objectId: number;
  objectType: string;
}

export interface PageableInfo {
  pageNumber: number;
  pageSize: number;
  sort: {
    sorted: boolean;
    unsorted: boolean;
    empty: boolean;
  };
  offset: number;
  paged: boolean;
  unpaged: boolean;
}

export interface SortInfo {
  sorted: boolean;
  unsorted: boolean;
  empty: boolean;
}

export interface UserHistorySocialNetworkResponse {
  statusCode: number;
  responseCode: string | null;
  responseMessage: string;
  data: {
    content: UserHistorySocialNetwork[];
    pageable: PageableInfo;
    totalPages: number;
    totalElements: number;
    last: boolean;
    size: number;
    number: number;
    sort: SortInfo;
    numberOfElements: number;
    first: boolean;
    empty: boolean;
  };
}

// Kiểu dữ liệu cho người dùng trong bài đăng
export interface PostUser {
  id: number;
  fullName: string;
  thumbnail: string | null;
  role?: string;
}

// Kiểu dữ liệu cho hashtag
export interface Hashtag {
  id?: number;
  name: string;
}

// Kiểu dữ liệu cho quyền riêng tư
export enum PrivacyLevel {
  PUBLIC = "PUBLIC",
  FRIEND = "FRIEND",
  ONLY_ME = "ONLY_ME"
}

// Kiểu dữ liệu cho bình luận
export interface Comment {
  id: number;
  postId: number;
  userId: number;
  user: PostUser;
  content: string;
  createdAt: string;
  likes: number;
  liked?: boolean;
  parentId: number | null;
  replies?: Comment[];
}

// Kiểu dữ liệu cho bài đăng
export interface Post {
  postSocialNetworkId: number;
  firstName: string;
  lastName: string;
  thumbnail: string;
  createdAt: string;
  email: string;
  content: string;
  hashTags: string[];
  likeCount: number;
  commentCount: number;
  shareCount: number;
  isLiked: boolean;
  isSaved: boolean;
  referPostAvailable: boolean;
  files: string[];
  postReferId: number | null;
  postRefer: Post | null;
  privacyPolicy: string;
  fileUrls: string[];
}

// Kiểu dữ liệu cho phản hồi API bài đăng
export interface PostResponse {
  statusCode: number;
  responseCode: string | null;
  responseMessage: string;
  data: {
    content: Post[];
    pageable: PageableInfo;
    totalPages: number;
    totalElements: number;
    last: boolean;
    size: number;
    number: number;
    sort: SortInfo;
    numberOfElements: number;
    first: boolean;
    empty: boolean;
  };
}

// Kiểu dữ liệu cho phản hồi API bình luận
export interface CommentResponse {
  statusCode: number;
  responseCode: string | null;
  responseMessage: string;
  data: {
    content: Comment[];
    pageable: PageableInfo;
    totalPages: number;
    totalElements: number;
    last: boolean;
    size: number;
    number: number;
    sort: SortInfo;
    numberOfElements: number;
    first: boolean;
    empty: boolean;
  };
}