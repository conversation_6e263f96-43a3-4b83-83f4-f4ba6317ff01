import Axios from "../../lib/Axios";

const getListFriendByCurrentSession = ({
                                           page = 0,
                                           size = 10
                                       }: {
    page: number;
    size: number;
}) => Axios.getRequest(`/user/friend-social-network`, {page, size});

const getListFriendByUsername = ({
                                     page = 0,
                                     size = 10,
                                     username = ''
                                 }: {
    page: number;
    size: number;
    username: string;
}) => Axios.getRequest(`/user/friend-social-network/profile/${username}`, {page, size});


const FriendServices = {
    getListFriendByCurrentSession,
    getListFriendByUsername
};

export default FriendServices;

