"use client";

import { useParams } from "next/navigation";
import { useEffect, useState } from "react";

import useUploadFile from "@/hook/useUploadFile";
import socialNetworkServices from "@/services/social-network/socialNetworkServices";
import { UserSocialNetwork } from "@/services/social-network/types/types";
import ProfileTabs from "../components/ProfileTabs";
import FriendsTabReadOnly from "./components/FriendsTabReadOnly";
import PlaceholderWidget from "./components/PlaceholderWidget";
import PostsTabReadOnly from "./components/PostsTabReadOnly";
import UserProfileHeaderReadOnly from "./components/UserProfileHeaderReadOnly";

export type UserData = Partial<UserSocialNetwork> & {
  avatarUrl?: string;
  coverUrl?: string;
  friendStatus?: string;
};

export default function EmailProfilePage() {
  // States cho trang profile
  const [activeTab, setActiveTab] = useState("posts");
  const [userData, setUserData] = useState<UserData>({});
  const [loadingUserData, setLoadingUserData] = useState(true);
  const { viewFile } = useUploadFile();
  const params = useParams();
  const email = params.email as string;

  // Gọi API để lấy dữ liệu người dùng theo email
  useEffect(() => {
    const fetchUserData = async () => {
      try {
        setLoadingUserData(true);
        // Gọi API lấy thông tin user theo email (sử dụng getUserSocialNetworkByUserName)
        const response = await socialNetworkServices.getUserSocialNetworkByUserName(email);
        const userSocialNetwork: UserSocialNetwork = response.data.data;
        let avatarUrl = "";
        let coverUrl = "";

        // Lấy URL cho avatar và cover photo
        if (userSocialNetwork.thumbnail) {
          try {
            const thumbnailUrl = await viewFile(
              userSocialNetwork.thumbnail,
              "social-network"
            );
            avatarUrl = thumbnailUrl as string;
          } catch (error) {
            console.error("Lỗi khi lấy URL cho avatar:", error);
          }
        }

        if (userSocialNetwork.background) {
          try {
            const backgroundUrl = await viewFile(
              userSocialNetwork.background,
              "social-network"
            );
            coverUrl = backgroundUrl as string;
          } catch (error) {
            console.error("Lỗi khi lấy URL cho cover photo:", error);
          }
        }

        setUserData({
          ...userSocialNetwork,
          avatarUrl,
          coverUrl
        });
      } catch (error) {
        console.error("Lỗi khi lấy dữ liệu người dùng:", error);
      } finally {
        setLoadingUserData(false);
      }
    };

    if (email) {
      fetchUserData();
    }
  }, [email]);

  return (
    <div className="min-h-screen bg-gray-50 pb-10">
      <UserProfileHeaderReadOnly
        userData={userData}
        loadingUserData={loadingUserData}
      />
      <div className="container relative mx-auto px-4">
        <div className="flex flex-col gap-6 lg:flex-row lg:items-start">
          <PlaceholderWidget
            className="self-start overflow-y-auto lg:sticky lg:top-20 w-full lg:w-1/4 px-2 z-40"
            userData={userData}
          />
          <div className="w-full lg:w-3/4">
            <ProfileTabs
              activeTab={activeTab}
              onTabChange={setActiveTab}
              tabs={[
                { id: "posts", label: "Bài viết" },
                { id: "friends", label: "Bạn bè" }
              ]}
            />
            {activeTab === "posts" && (
              <PostsTabReadOnly userData={userData} />
            )}
            {activeTab === "friends" && <FriendsTabReadOnly userData={userData} />}
          </div>
        </div>
      </div>
    </div>
  );
}