"use client";

import { cn } from "@/lib/utils";
import {
  FaChartLine,
  FaUsers
} from "react-icons/fa";
import type { UserData } from "../page";

interface PlaceholderWidgetProps {
  className?: string;
  userData: UserData;
}

// Helper function to get activity level based on posts and likes
const getActivityLevel = (posts: number, likes: number) => {
  const totalActivity = posts + likes;
  if (totalActivity === 0) return { level: "Mới tham gia", color: "text-gray-500", bgColor: "bg-gray-100" };
  if (totalActivity < 5) return { level: "Ít hoạt động", color: "text-yellow-600", bgColor: "bg-yellow-100" };
  if (totalActivity < 20) return { level: "Hoạt động vừa", color: "text-blue-600", bgColor: "bg-blue-100" };
  return { level: "Rất tích c<PERSON>c", color: "text-green-600", bgColor: "bg-green-100" };
};

// Helper function to get friend status display
const getFriendStatusDisplay = (status: string) => {
  switch (status) {
    case "FRIEND":
      return { text: "Bạn bè", color: "text-green-600", bgColor: "bg-green-100", icon: <FaUsers className="text-green-600" /> };
    case "PENDING":
      return { text: "Đang chờ", color: "text-yellow-600", bgColor: "bg-yellow-100", icon: <FaUsers className="text-yellow-600" /> };
    case "NOT_FRIEND":
      return { text: "Chưa kết bạn", color: "text-gray-600", bgColor: "bg-gray-100", icon: <FaUsers className="text-gray-600" /> };
    default:
      return { text: "Không xác định", color: "text-gray-600", bgColor: "bg-gray-100", icon: <FaUsers className="text-gray-600" /> };
  }
};

export default function PlaceholderWidget({ className, userData }: PlaceholderWidgetProps) {
  const activityLevel = getActivityLevel(userData.numberOfPost || 0, userData.numberOfLike || 0);
  const friendStatus = getFriendStatusDisplay(userData.friendStatus || "NOT_FRIEND");

  return (
    <div className={cn("space-y-4", className)}>
      {/* Profile Overview */}
      <div className="rounded-lg border border-gray-200 bg-white shadow-lg overflow-hidden">
        <div className="bg-gray-50 p-4 border-b border-gray-200">
          <h3 className="flex items-center gap-2 text-lg font-semibold text-gray-800">
            <FaInfoCircle className="text-gray-600" />
            Tổng quan profile
          </h3>
        </div>
        <div className="p-4 space-y-4">
          {/* Activity Level */}
          <div className="text-center">
            <div className="flex items-center justify-center gap-2 mb-2">
              <FaChartLine className="text-gray-600" />
              <span className="text-sm font-medium text-gray-700">Mức độ hoạt động</span>
            </div>
            <div className={`inline-flex px-4 py-2 rounded-full text-sm font-medium ${activityLevel.bgColor} ${activityLevel.color}`}>
              {activityLevel.level}
            </div>
            <p className="text-xs text-gray-500 mt-1">
              Dựa trên {(userData.numberOfPost || 0) + (userData.numberOfLike || 0)} hoạt động
            </p>
          </div>

          {/* Divider */}
          <div className="border-t border-gray-200"></div>

          {/* Friend Status */}
          <div className="text-center">
            <div className="flex items-center justify-center gap-2 mb-2">
              <FaUsers className="text-gray-600" />
              <span className="text-sm font-medium text-gray-700">Trạng thái kết bạn</span>
            </div>
            <div className="mb-2">
              {friendStatus.icon}
            </div>
            <div className={`inline-flex px-4 py-2 rounded-full text-sm font-medium ${friendStatus.bgColor} ${friendStatus.color}`}>
              {friendStatus.text}
            </div>
            <p className="text-xs text-gray-500 mt-1">
              Với {userData.firstName}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
