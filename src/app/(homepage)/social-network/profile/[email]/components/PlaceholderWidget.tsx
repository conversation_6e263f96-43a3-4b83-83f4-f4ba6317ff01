"use client";

import { cn } from "@/lib/utils";
import {
  FaChartLine,
  FaEnvelope,
  FaInfoCircle,
  FaStar,
  FaUser,
  FaUsers
} from "react-icons/fa";
import type { UserData } from "../page";

interface PlaceholderWidgetProps {
  className?: string;
  userData: UserData;
}

// Helper function to get activity level based on posts and likes
const getActivityLevel = (posts: number, likes: number) => {
  const totalActivity = posts + likes;
  if (totalActivity === 0) return { level: "Mới tham gia", color: "text-gray-500", bgColor: "bg-gray-100" };
  if (totalActivity < 5) return { level: "Ít hoạt động", color: "text-yellow-600", bgColor: "bg-yellow-100" };
  if (totalActivity < 20) return { level: "Hoạt động vừa", color: "text-blue-600", bgColor: "bg-blue-100" };
  return { level: "<PERSON><PERSON><PERSON> t<PERSON>ch c<PERSON>", color: "text-green-600", bgColor: "bg-green-100" };
};

// Helper function to get friend status display
const getFriendStatusDisplay = (status: string) => {
  switch (status) {
    case "FRIEND":
      return { text: "Bạn bè", color: "text-green-600", bgColor: "bg-green-100", icon: <FaUsers className="text-green-600" /> };
    case "PENDING":
      return { text: "Đang chờ", color: "text-yellow-600", bgColor: "bg-yellow-100", icon: <FaUsers className="text-yellow-600" /> };
    case "NOT_FRIEND":
      return { text: "Chưa kết bạn", color: "text-gray-600", bgColor: "bg-gray-100", icon: <FaUsers className="text-gray-600" /> };
    default:
      return { text: "Không xác định", color: "text-gray-600", bgColor: "bg-gray-100", icon: <FaUsers className="text-gray-600" /> };
  }
};

export default function PlaceholderWidget({ className, userData }: PlaceholderWidgetProps) {
  const activityLevel = getActivityLevel(userData.numberOfPost || 0, userData.numberOfLike || 0);

  return (
    <div className={cn("space-y-4", className)}>
      {/* User Profile Stats */}
      <div className="rounded-lg border border-gray-200 bg-white shadow-lg overflow-hidden">
        <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-4">
          <h3 className="flex items-center gap-2 text-lg font-semibold text-white">
            <FaUser className="text-white/80" />
            Thông tin profile
          </h3>
        </div>
        <div className="p-4 space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-gray-600">Tên hiển thị</span>
            <span className="font-medium text-gray-800">
              {userData.fullName || `${userData.firstName} ${userData.lastName}`}
            </span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-gray-600">Email</span>
            <span className="font-medium text-gray-800 text-sm">
              {userData.email}
            </span>
          </div>
          {userData.job && (
            <div className="flex items-center justify-between">
              <span className="text-gray-600">Công việc</span>
              <span className="font-medium text-gray-800">{userData.job}</span>
            </div>
          )}
          {userData.education && (
            <div className="flex items-center justify-between">
              <span className="text-gray-600">Học vấn</span>
              <span className="font-medium text-gray-800">{userData.education}</span>
            </div>
          )}
        </div>
      </div>

      {/* Activity Stats */}
      <div className="rounded-lg border border-gray-200 bg-white shadow-lg overflow-hidden">
        <div className="bg-gradient-to-r from-green-500 to-teal-600 p-4">
          <h3 className="flex items-center gap-2 text-lg font-semibold text-white">
            <FaChartLine className="text-white/80" />
            Thống kê hoạt động
          </h3>
        </div>
        <div className="p-4 space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-gray-600">Số bài viết</span>
            <span className="font-bold text-blue-600">{userData.numberOfPost || 0}</span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-gray-600">Lượt thích nhận được</span>
            <span className="font-bold text-red-600">{userData.numberOfLike || 0}</span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-gray-600">Số bạn bè</span>
            <span className="font-bold text-green-600">{userData.numberOfFriend || 0}</span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-gray-600">Mức độ hoạt động</span>
            <span className={`px-2 py-1 rounded-full text-xs font-medium ${activityLevel.bgColor} ${activityLevel.color}`}>
              {activityLevel.level}
            </span>
          </div>
        </div>
      </div>

      {/* Contact Info */}
      <div className="rounded-lg border border-gray-200 bg-white shadow-lg overflow-hidden">
        <div className="bg-gradient-to-r from-orange-500 to-red-600 p-4">
          <h3 className="flex items-center gap-2 text-lg font-semibold text-white">
            <FaInfoCircle className="text-white/80" />
            Thông tin liên hệ
          </h3>
        </div>
        <div className="p-4 space-y-3">
          <div className="flex items-center gap-2">
            <FaEnvelope className="text-gray-500" />
            <span className="text-gray-600 text-sm">{userData.email}</span>
          </div>
          {userData.phoneNumber && (
            <div className="flex items-center gap-2">
              <span className="text-gray-500">📞</span>
              <span className="text-gray-600 text-sm">{userData.phoneNumber}</span>
            </div>
          )}
          {userData.address && (
            <div className="flex items-center gap-2">
              <span className="text-gray-500">📍</span>
              <span className="text-gray-600 text-sm">{userData.address}</span>
            </div>
          )}
          {userData.orgName && (
            <div className="flex items-center gap-2">
              <span className="text-gray-500">🏢</span>
              <span className="text-gray-600 text-sm">{userData.orgName}</span>
            </div>
          )}
        </div>
      </div>

      {/* Additional Info */}
      <div className="rounded-lg border border-gray-200 bg-gradient-to-br from-indigo-50 to-purple-50 p-4">
        <div className="text-center">
          <div className="mb-2">
            <FaStar className="mx-auto text-2xl text-indigo-500" />
          </div>
          <h4 className="font-semibold text-gray-800 mb-2">
            Profile của {userData.firstName}
          </h4>
          <p className="text-sm text-gray-600">
            {userData.bio || "Chưa có thông tin giới thiệu bản thân."}
          </p>
        </div>
      </div>
    </div>
  );
}
