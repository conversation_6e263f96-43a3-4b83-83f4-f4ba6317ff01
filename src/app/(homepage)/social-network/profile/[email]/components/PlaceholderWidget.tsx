"use client";

import { cn } from "@/lib/utils";
import {
  FaChartLine,
  FaEnvelope,
  FaInfoCircle,
  FaStar,
  FaUsers
} from "react-icons/fa";
import type { UserData } from "../page";

interface PlaceholderWidgetProps {
  className?: string;
  userData: UserData;
}

// Helper function to get activity level based on posts and likes
const getActivityLevel = (posts: number, likes: number) => {
  const totalActivity = posts + likes;
  if (totalActivity === 0) return { level: "Mới tham gia", color: "text-gray-500", bgColor: "bg-gray-100" };
  if (totalActivity < 5) return { level: "Ít hoạt động", color: "text-yellow-600", bgColor: "bg-yellow-100" };
  if (totalActivity < 20) return { level: "Hoạt động vừa", color: "text-blue-600", bgColor: "bg-blue-100" };
  return { level: "Rất tích cực", color: "text-green-600", bgColor: "bg-green-100" };
};

// Helper function to get friend status display
const getFriendStatusDisplay = (status: string) => {
  switch (status) {
    case "FRIEND":
      return { text: "Bạn bè", color: "text-green-600", bgColor: "bg-green-100", icon: <FaUsers className="text-green-600" /> };
    case "PENDING":
      return { text: "Đang chờ", color: "text-yellow-600", bgColor: "bg-yellow-100", icon: <FaUsers className="text-yellow-600" /> };
    case "NOT_FRIEND":
      return { text: "Chưa kết bạn", color: "text-gray-600", bgColor: "bg-gray-100", icon: <FaUsers className="text-gray-600" /> };
    default:
      return { text: "Không xác định", color: "text-gray-600", bgColor: "bg-gray-100", icon: <FaUsers className="text-gray-600" /> };
  }
};

export default function PlaceholderWidget({ className, userData }: PlaceholderWidgetProps) {
  const activityLevel = getActivityLevel(userData.numberOfPost || 0, userData.numberOfLike || 0);

  return (
    <div className={cn("space-y-4", className)}>
      {/* Activity Level */}
      <div className="rounded-lg border border-gray-200 bg-white shadow-lg overflow-hidden">
        <div className="bg-gradient-to-r from-green-500 to-teal-600 p-4">
          <h3 className="flex items-center gap-2 text-lg font-semibold text-white">
            <FaChartLine className="text-white/80" />
            Mức độ hoạt động
          </h3>
        </div>
        <div className="p-4">
          <div className="text-center">
            <div className={`inline-flex px-4 py-2 rounded-full text-sm font-medium ${activityLevel.bgColor} ${activityLevel.color}`}>
              {activityLevel.level}
            </div>
            <p className="text-xs text-gray-500 mt-2">
              Dựa trên {(userData.numberOfPost || 0) + (userData.numberOfLike || 0)} hoạt động
            </p>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="rounded-lg border border-gray-200 bg-white shadow-lg overflow-hidden">
        <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-4">
          <h3 className="flex items-center gap-2 text-lg font-semibold text-white">
            <FaUsers className="text-white/80" />
            Tương tác
          </h3>
        </div>
        <div className="p-4 space-y-3">
          <button className="w-full flex items-center justify-center gap-2 bg-blue-50 hover:bg-blue-100 text-blue-600 py-2 px-4 rounded-lg transition-colors">
            <FaUsers className="text-sm" />
            <span className="text-sm font-medium">Xem bạn bè</span>
          </button>
          <button className="w-full flex items-center justify-center gap-2 bg-green-50 hover:bg-green-100 text-green-600 py-2 px-4 rounded-lg transition-colors">
            <FaEnvelope className="text-sm" />
            <span className="text-sm font-medium">Gửi tin nhắn</span>
          </button>
        </div>
      </div>

      {/* Profile Insights */}
      <div className="rounded-lg border border-gray-200 bg-white shadow-lg overflow-hidden">
        <div className="bg-gradient-to-r from-orange-500 to-red-600 p-4">
          <h3 className="flex items-center gap-2 text-lg font-semibold text-white">
            <FaInfoCircle className="text-white/80" />
            Thông tin thêm
          </h3>
        </div>
        <div className="p-4 space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-gray-600">Tham gia từ</span>
            <span className="text-sm text-gray-800">Gần đây</span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-gray-600">Hoạt động cuối</span>
            <span className="text-sm text-gray-800">Hôm nay</span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-gray-600">Trạng thái</span>
            <span className="text-sm text-green-600">● Online</span>
          </div>
        </div>
      </div>

      {/* Welcome Message */}
      <div className="rounded-lg border border-gray-200 bg-gradient-to-br from-indigo-50 to-purple-50 p-4">
        <div className="text-center">
          <div className="mb-2">
            <FaStar className="mx-auto text-2xl text-indigo-500" />
          </div>
          <h4 className="font-semibold text-gray-800 mb-2">
            Khám phá profile
          </h4>
          <p className="text-sm text-gray-600">
            Xem các bài viết và hoạt động của {userData.firstName} để hiểu thêm về họ.
          </p>
        </div>
      </div>
    </div>
  );
}
