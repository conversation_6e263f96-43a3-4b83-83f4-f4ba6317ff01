"use client";

import { 
  FaQuoteL<PERSON>, 
  FaLightbulb, 
  FaHeart, 
  FaStar,
  FaGem,
  FaLeaf,
  FaRocket,
  FaMagic
} from "react-icons/fa";
import { cn } from "@/lib/utils";

interface PlaceholderWidgetProps {
  className?: string;
}

const inspirationalQuotes = [
  {
    text: "Thành công không phải là chìa khóa của hạnh phúc. Hạnh phúc là chìa khóa của thành công.",
    author: "<PERSON>",
    icon: <FaHeart className="text-red-500" />
  },
  {
    text: "Cách duy nhất để làm việc tuyệt vời là yêu thích những gì bạn làm.",
    author: "<PERSON>",
    icon: <FaRocket className="text-blue-500" />
  },
  {
    text: "Đừng chờ đợi cơ hội. <PERSON><PERSON><PERSON> tạo ra chúng.",
    author: "<PERSON>",
    icon: <FaMagic className="text-purple-500" />
  },
  {
    text: "Hành trình ngàn dặm bắt đầu bằng một bước chân.",
    author: "Lão Tử",
    icon: <FaLeaf className="text-green-500" />
  },
  {
    text: "Giáo dục là vũ khí mạnh nhất bạn có thể sử dụng để thay đổi thế giới.",
    author: "Nelson Mandela",
    icon: <FaGem className="text-yellow-500" />
  }
];

const tips = [
  {
    title: "Kết nối ý nghĩa",
    content: "Hãy tương tác chân thành với những người có cùng sở thích và mục tiêu.",
    icon: <FaHeart className="text-pink-500" />
  },
  {
    title: "Chia sẻ kiến thức",
    content: "Những gì bạn biết có thể giúp ích cho người khác. Đừng ngần ngại chia sẻ!",
    icon: <FaLightbulb className="text-yellow-500" />
  },
  {
    title: "Học hỏi liên tục",
    content: "Mỗi ngày là một cơ hội để học điều gì đó mới và phát triển bản thân.",
    icon: <FaStar className="text-blue-500" />
  }
];

export default function PlaceholderWidget({ className }: PlaceholderWidgetProps) {
  // Chọn ngẫu nhiên một quote và một tip
  const randomQuote = inspirationalQuotes[Math.floor(Math.random() * inspirationalQuotes.length)];
  const randomTip = tips[Math.floor(Math.random() * tips.length)];

  return (
    <div className={cn("space-y-4", className)}>
      {/* Quote Card */}
      <div className="rounded-lg border border-gray-200 bg-white shadow-lg overflow-hidden">
        <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-4">
          <h3 className="flex items-center gap-2 text-lg font-semibold text-white">
            <FaQuoteLeft className="text-white/80" />
            Cảm hứng hôm nay
          </h3>
        </div>
        <div className="p-4">
          <div className="flex items-start gap-3">
            <div className="mt-1">
              {randomQuote.icon}
            </div>
            <div className="flex-1">
              <p className="text-gray-700 italic leading-relaxed mb-2">
                "{randomQuote.text}"
              </p>
              <p className="text-sm font-medium text-gray-500">
                — {randomQuote.author}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Tip Card */}
      <div className="rounded-lg border border-gray-200 bg-white shadow-lg overflow-hidden">
        <div className="bg-gradient-to-r from-green-500 to-teal-600 p-4">
          <h3 className="flex items-center gap-2 text-lg font-semibold text-white">
            <FaLightbulb className="text-white/80" />
            Mẹo hữu ích
          </h3>
        </div>
        <div className="p-4">
          <div className="flex items-start gap-3">
            <div className="mt-1">
              {randomTip.icon}
            </div>
            <div className="flex-1">
              <h4 className="font-semibold text-gray-800 mb-2">
                {randomTip.title}
              </h4>
              <p className="text-gray-600 leading-relaxed">
                {randomTip.content}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Stats Card */}
      <div className="rounded-lg border border-gray-200 bg-white shadow-lg overflow-hidden">
        <div className="bg-gradient-to-r from-orange-500 to-red-600 p-4">
          <h3 className="flex items-center gap-2 text-lg font-semibold text-white">
            <FaStar className="text-white/80" />
            Thống kê thú vị
          </h3>
        </div>
        <div className="p-4 space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-gray-600">Người dùng hoạt động</span>
            <span className="font-bold text-orange-600">1,234+</span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-gray-600">Bài viết hôm nay</span>
            <span className="font-bold text-green-600">89</span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-gray-600">Kết nối mới</span>
            <span className="font-bold text-blue-600">156</span>
          </div>
        </div>
      </div>

      {/* Welcome Message */}
      <div className="rounded-lg border border-gray-200 bg-gradient-to-br from-indigo-50 to-purple-50 p-4">
        <div className="text-center">
          <div className="mb-2">
            <FaGem className="mx-auto text-2xl text-indigo-500" />
          </div>
          <h4 className="font-semibold text-gray-800 mb-2">
            Chào mừng đến với cộng đồng!
          </h4>
          <p className="text-sm text-gray-600">
            Khám phá, kết nối và chia sẻ những trải nghiệm tuyệt vời cùng nhau.
          </p>
        </div>
      </div>
    </div>
  );
}
