"use client";

import {
  FaBriefcase,
  FaBuilding,
  FaCalendarAlt,
  FaEnvelope,
  FaGraduationCap,
  FaMapMarkerAlt,
  FaPhone,
  FaVenusMars
} from "react-icons/fa";
import { MdVerified } from "react-icons/md";
import { RiBuilding2Line } from "react-icons/ri";

import {
  formatDateForDisplay
} from "../../../utils/dateUtils";

import DefaultAvatar from "@/components/ui/DefaultAvatar";
import DefaultCover from "@/components/ui/DefaultCover";
import Image from "next/image";
import type { UserData } from "../page";

interface UserProfileHeaderReadOnlyProps {
  userData: UserData;
  loadingUserData: boolean;
}

export default function UserProfileHeaderReadOnly({
  userData,
  loadingUserData
}: UserProfileHeaderReadOnlyProps) {

  if (loadingUserData)
    return (
      <>
        {/* Cover Photo Skeleton */}
        <div className="relative h-64 w-full animate-pulse overflow-hidden bg-gray-200 md:h-80"></div>

        {/* Profile Header Skeleton */}
        <div className="container relative mx-auto px-4">
          <div className="-mt-20 mb-6 rounded-lg bg-white p-6 shadow-md md:p-8">
            <div className="flex flex-col items-center md:items-start gap-6 md:flex-row">
              {/* Avatar Skeleton */}
              <div className="relative">
                <div className="relative h-32 w-32 animate-pulse overflow-hidden rounded-full border-4 border-white bg-gray-300 md:h-40 md:w-40"></div>
              </div>

              {/* User Info Skeleton */}
              <div className="flex-1">
                <div className="flex w-full flex-col-reverse justify-between gap-4 md:flex-row md:items-center">
                  <div>
                    {/* Name Skeleton */}
                    <div className="mb-2 h-8 w-48 animate-pulse rounded-md bg-gray-300"></div>
                    {/* Role Skeleton */}
                    <div className="h-4 w-32 animate-pulse rounded-md bg-gray-300"></div>
                  </div>
                </div>

                {/* Bio Skeleton */}
                <div className="mt-4">
                  <div className="h-16 w-full animate-pulse rounded-md bg-gray-300"></div>
                </div>

                {/* User Details Skeleton */}
                <div className="mt-4 flex flex-wrap gap-x-6 gap-y-2">
                  <div className="h-6 w-32 animate-pulse rounded-md bg-gray-300"></div>
                  <div className="h-6 w-32 animate-pulse rounded-md bg-gray-300"></div>
                  <div className="h-6 w-32 animate-pulse rounded-md bg-gray-300"></div>
                </div>

                {/* Stats Skeleton */}
                <div className="mt-6 flex flex-wrap gap-6">
                  <div className="text-center">
                    <div className="mx-auto mb-1 h-8 w-8 animate-pulse rounded-md bg-gray-300"></div>
                    <div className="h-4 w-16 animate-pulse rounded-md bg-gray-300"></div>
                  </div>
                  <div className="text-center">
                    <div className="mx-auto mb-1 h-8 w-8 animate-pulse rounded-md bg-gray-300"></div>
                    <div className="h-4 w-16 animate-pulse rounded-md bg-gray-300"></div>
                  </div>
                  <div className="text-center">
                    <div className="mx-auto mb-1 h-8 w-8 animate-pulse rounded-md bg-gray-300"></div>
                    <div className="h-4 w-16 animate-pulse rounded-md bg-gray-300"></div>
                  </div>
                  <div className="text-center">
                    <div className="mx-auto mb-1 h-8 w-8 animate-pulse rounded-md bg-gray-300"></div>
                    <div className="h-4 w-16 animate-pulse rounded-md bg-gray-300"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </>
    );

  return (
    <>
      {/* Cover Image */}
      <div className="relative h-64 w-full overflow-hidden md:h-80">
        {userData.coverUrl ? (
          <Image
            src={userData.coverUrl}
            alt="Cover"
            fill
            className="object-cover"
          />
        ) : (
          <DefaultCover height="100%" />
        )}
      </div>

      {/* Profile Header */}
      <div className="container relative mx-auto px-4">
        <div className="-mt-20 mb-6 rounded-lg bg-white p-6 shadow-md md:p-8">
          <div className="flex flex-col items-center md:items-start gap-6 md:flex-row">
            {/* Avatar */}
            <div className="relative">
              <div className="relative h-32 w-32 overflow-hidden rounded-full border-4 border-white md:h-40 md:w-40">
                {userData.avatarUrl ? (
                  <Image
                    src={userData.avatarUrl}
                    alt={
                      userData.fullName ||
                      `${userData.firstName} ${userData.lastName}`
                    }
                    fill
                    className="object-cover"
                  />
                ) : (
                  <DefaultAvatar
                    name={
                      userData.fullName ||
                      `${userData.firstName} ${userData.lastName}` ||
                      "User"
                    }
                    size={200}
                  />
                )}
              </div>
            </div>

            {/* User Info */}
            <div className="flex-1">
              <div className="flex w-full flex-col-reverse justify-between gap-4 md:flex-row md:items-center">
                <div>
                  <h1 className="flex text-2xl font-bold md:text-3xl">
                    {userData.fullName ||
                      `${userData.firstName} ${userData.lastName}`}
                    {userData.orgName && (
                      <div className="ml-2 flex items-center rounded text-xl text-blue-600">
                        <MdVerified />
                        <div className="ml-2 flex items-center rounded-full bg-green-500 px-3 py-1 text-sm font-medium text-white shadow-sm">
                          <RiBuilding2Line></RiBuilding2Line>
                          <span className="ml-1"> {userData.orgName}</span>
                        </div>
                      </div>
                    )}
                  </h1>
                  <p className="font-medium text-blue-600">{userData.job}</p>
                </div>
              </div>

              {/* User Bio */}
              <div className="mt-4">
                <p className="text-gray-700">{userData.bio}</p>
              </div>

              {/* User Details */}
              <div className="mt-4 flex flex-wrap gap-x-6 gap-y-2 text-gray-600">
                {userData.address && (
                  <div className="flex items-center gap-2">
                    <FaMapMarkerAlt className="text-gray-500" />
                    <span>{userData.address}</span>
                  </div>
                )}
                {userData.education && (
                  <div className="flex items-center gap-2">
                    <FaGraduationCap className="text-gray-500" />
                    <span>{userData.education}</span>
                  </div>
                )}
                {userData.job && (
                  <div className="flex items-center gap-2">
                    <FaBriefcase className="text-gray-500" />
                    <span>{userData.job}</span>
                  </div>
                )}
                {userData.phoneNumber && (
                  <div className="flex items-center gap-2">
                    <FaPhone className="text-gray-500" />
                    <span>{userData.phoneNumber}</span>
                  </div>
                )}
                {userData.dateOfBirth && (
                  <div className="flex items-center gap-2">
                    <FaCalendarAlt className="text-gray-500" />
                    <span>{formatDateForDisplay(userData.dateOfBirth)}</span>
                  </div>
                )}
                {userData.gender && (
                  <div className="flex items-center gap-2">
                    <FaVenusMars className="text-gray-500" />
                    <span>{userData.gender}</span>
                  </div>
                )}
                {userData.orgName && (
                  <div className="flex items-center gap-2">
                    <FaBuilding className="text-gray-500" />
                    <span>{userData.orgName}</span>
                  </div>
                )}
                <div className="flex items-center gap-2">
                  <FaEnvelope className="text-gray-500" />
                  <span>{userData.email}</span>
                </div>
              </div>

              {/* Stats */}
              <div className="mt-6 flex flex-wrap gap-6">
                <div className="text-center">
                  <div className="text-xl font-bold">
                    {userData.numberOfPost}
                  </div>
                  <div className="text-sm text-gray-600">Bài viết</div>
                </div>
                <div className="text-center">
                  <div className="text-xl font-bold">
                    {userData.numberOfFriend}
                  </div>
                  <div className="text-sm text-gray-600">Bạn bè</div>
                </div>
                <div className="text-center">
                  <div className="text-xl font-bold">
                    {userData.numberOfLike}
                  </div>
                  <div className="text-sm text-gray-600">Lượt thích</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
